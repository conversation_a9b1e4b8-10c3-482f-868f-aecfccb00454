import os,json,re,uuid,logging,asyncio
from typing import Optional, List, Dict, Any

from fastapi import FastAPI, Request, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse, HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from pydantic import BaseModel

from langchain_ollama import ChatOllama
from langchain.schema import HumanMessage, AIMessage, SystemMessage
from tts_voicevox import TTS

# ------------------ 一般設定 ------------------

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("app")

app = FastAPI()
app.add_middleware(
    SessionMiddleware,
    secret_key=os.environ.get("FLASK_SECRET_KEY", "my-dev-secret-key"),
    same_site="lax",
)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允許所有來源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
from fastapi.staticfiles import StaticFiles
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/templates", StaticFiles(directory="templates"), name="templates")

chat_model = ChatOllama(model="gemma3")
tts = TTS(speaker_id=58)  # 使用 VoiceVox 語音合成

MAX_MEMORY = 8  # 最多保留的對話記憶數量
MAX_FACTS_PER_USER = 20  # 每個使用者最多記憶的事實數量
MEMORY_FILE = "memory.json"
AUDIO_DIR = "static/audio"
DEFAULT_INTIMACY = 50  # 初始親密度
MAX_INTIMACY = 100
MIN_INTIMACY = 0

os.makedirs(AUDIO_DIR, exist_ok=True)

# 檔案讀寫鎖，避免多請求同時寫 memory.json 造成毀損
MEMORY_LOCK = asyncio.Lock()

# ------------------ 資料模型 ------------------

class ChatPayload(BaseModel):
    message: str

class IntimacyUpdatePayload(BaseModel):
    amount: int

# ------------------ 工具：非同步包裝 ------------------

async def _to_thread(func, *args, **kwargs):
    """把阻塞/CPU 或外部 I/O 的呼叫丟到 thread pool。"""
    return await asyncio.to_thread(func, *args, **kwargs)

# ------------------ 記憶與親密度系統 ------------------

async def load_long_term_memory() -> Dict[str, Any]:
    async with MEMORY_LOCK:
        if not os.path.exists(MEMORY_FILE):
            return {}
        try:
            def _load():
                with open(MEMORY_FILE, "r", encoding="utf-8") as f:
                    return json.load(f)
            return await _to_thread(_load)
        except Exception as e:
            logger.error(f"讀取記憶失敗：{e}")
            return {}

async def save_long_term_memory(data: Dict[str, Any]) -> None:
    async with MEMORY_LOCK:
        def _save():
            with open(MEMORY_FILE, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        try:
            await _to_thread(_save)
        except Exception as e:
            logger.error(f"儲存記憶失敗：{e}")

async def get_user_data(user_id: str) -> Dict[str, Any]:
    memory = await load_long_term_memory()
    if user_id not in memory:
        memory[user_id] = {"facts": [], "intimacy": DEFAULT_INTIMACY}
        await save_long_term_memory(memory)
    return memory[user_id]

async def set_user_data(user_id: str, data: Dict[str, Any]) -> None:
    memory = await load_long_term_memory()
    memory[user_id] = data
    await save_long_term_memory(memory)

async def append_memory(user_id: str, new_fact: str) -> None:
    user_data = await get_user_data(user_id)
    if new_fact not in user_data["facts"]:
        user_data["facts"].append(new_fact)
        if len(user_data["facts"]) > MAX_FACTS_PER_USER:
            user_data["facts"] = user_data["facts"][-MAX_FACTS_PER_USER:]
        await set_user_data(user_id, user_data)
        logger.info(f"新增記憶給使用者 {user_id}: {new_fact}")

async def get_memory_prompt(user_id: str) -> str:
    user_data = await get_user_data(user_id)
    facts: List[str] = user_data.get("facts", [])
    if facts:
        mem_lines = "\n".join(f"- {fact}" for fact in facts)
        return (
            "這是你記住使用者的資訊：\n"
            f"{mem_lines}\n"
            "請自然地融入對話中，但不要主動說出你記得這些事喔。\n"
        )
    return ""

async def adjust_intimacy(user_id: str, amount: int) -> int:
    user_data = await get_user_data(user_id)
    new_val = max(MIN_INTIMACY, min(MAX_INTIMACY, user_data["intimacy"] + amount))
    user_data["intimacy"] = new_val
    await set_user_data(user_id, user_data)
    logger.info(f"使用者 {user_id} 親密度調整為 {new_val}")
    return new_val

def get_intimacy_level_name(value: int) -> str:
    if value < 30:
        return "冷淡期"
    if value < 60:
        return "普通期"
    if value < 90:
        return "親密期"
    return "羈絆期"

def build_intimacy_tier_prompt(value: int) -> str:
    if value < 30:
        return "目前你對使用者有些冷淡。請簡短、保持距離地回覆，少用暱稱與撒嬌語氣。"
    if value < 60:
        return "你與使用者關係普通。正常、友善地回覆即可，偶爾可以輕鬆一點。"
    if value < 90:
        return "你與使用者已相當親近。回覆時可以多用撒嬌語氣、暱稱與可愛表情，主動關心對方。"
    return "你與使用者擁有深厚羈絆。請用特別甜美、專屬且真誠的語氣回覆，偶爾主動提出貼心建議與小驚喜。"

# ------------------ LLM 與親密度評估 ------------------

async def extract_facts_from_llm(message: str) -> List[str]:
    prompt = f"""
你是一個會從使用者對話中提取記憶的助手。請從以下訊息中提取出「摘要」與「記憶事實」。
只回傳 JSON 結構如下：
{{
  "summary": "...",
  "facts": ["...", "..."]
}}
訊息內容：
「{message}」
"""
    try:
        # ChatOllama 多半是同步呼叫，包 thread pool
        result = await _to_thread(chat_model.invoke, [HumanMessage(content=prompt)])
        m = re.search(r"\{.*\}", result.content, re.DOTALL)
        if m:
            data = json.loads(m.group(0))
            return data.get("facts", []) or []
    except Exception as e:
        logger.error(f"extract_facts_from_llm 失敗：{e}")
    return []

async def evaluate_intimacy_from_llm(user_message: str, bot_reply: str, current_intimacy: int) -> int:
    prompt = f"""
你是「對話親密度影響」的裁判。請綜合使用者訊息與機器人回覆，評估這次互動對親密度的變化。
回傳 JSON，整數介於 -2 到 +2。

規則（越高越親密）：
- 明確讚美、撒嬌、示好、感謝、分享私事或脆弱 → +1 ~ +2
- 普通閒聊或資訊型問題 → 0
- 明確拒絕、批評、貶低、嘲諷 → -1 ~ -2
- 若雙方語氣一致偏甜/親暱，可適度提高；若機器人語氣冷淡、拒絕，則降低。
- 不要因為單一正向詞就極端加分；請考慮上下文完整語意。

輸入：
使用者：「{user_message}」
機器人回覆：「{bot_reply}」
目前親密度：{current_intimacy}

只回傳 JSON 結構如下:
{{
  "intimacy_change": -2
}}
"""
    try:
        result = await _to_thread(chat_model.invoke, [HumanMessage(content=prompt)])
        m = re.search(r"\{.*\}", result.content, re.DOTALL)
        if m:
            data = json.loads(m.group(0))
            change = int(data.get("intimacy_change", 0))
            return max(-2, min(2, change))
    except Exception as e:
        logger.error(f"evaluate_intimacy_from_llm 失敗：{e}")
    return 0

# ------------------ 關鍵字/情緒標籤 ------------------

POSITIVE_PAT = re.compile(r"(謝謝|喜歡你|愛你|太棒|好棒|可愛|真貼心|抱抱|想你)")
NEGATIVE_PAT = re.compile(r"(不喜歡你|討厭你|爛|閉嘴|走開|煩|生氣|滾)")
NEUTRAL_PAT  = re.compile(r"(嗯|哦|好|OK|好的)[!！。.\s]*$", re.I)

def keyword_intimacy_fallback(text: str) -> int:
    tx = text.strip()
    if POSITIVE_PAT.search(tx):
        return 1
    if NEGATIVE_PAT.search(tx):
        return -1
    if NEUTRAL_PAT.search(tx):
        return 0
    return 0

def extract_emotion_tag(text: str) -> Optional[str]:
    tags = re.findall(r"\[emotion:(\w+)\]", text, flags=re.I)
    return tags[-1].lower() if tags else None

def emotion_weight(emotion: Optional[str]) -> int:
    if not emotion:
        return 0
    table = {"joy": 1, "cute": 1, "shy": 0, "neutral": 0, "sad": -1, "angry": -1}
    return table.get(emotion, 0)

# ------------------ regex 備援擷取記憶 ------------------

def extract_facts(message: str) -> List[str]:
    facts: List[str] = []

    m = re.search(r"(我叫|我的名字是)(\w+)", message)
    if m:
        facts.append(f"他的名字是{m.group(2)}")

    m = re.search(r"我喜歡(\w+)", message)
    if m:
        facts.append(f"他喜歡{m.group(1)}")

    m = re.search(r"我討厭(\w+)", message)
    if m:
        facts.append(f"他討厭{m.group(1)}")

    # 修正：\d{1,3}（原本是 \d{{1,3}})
    m = re.search(r"我.*?(\d{1,3})\s*歲", message)
    if m:
        facts.append(f"他{m.group(1)}歲")

    m = re.search(r"我住在(\w+)", message)
    if m:
        facts.append(f"他住在{m.group(1)}")

    if "我是" in message:
        for job in ["老師", "學生", "工程師", "設計師"]:
            if job in message:
                facts.append(f"他是{job}")

    m = re.search(r"我覺得(\w+)", message)
    if m:
        facts.append(f"他覺得{m.group(1)}")

    if "我今天" in message:
        for emo in ["開心", "難過", "累", "生氣", "放鬆"]:
            if emo in message:
                facts.append(f"他今天{emo}")
    return facts

# ------------------ 語音快取清理 ------------------

def clean_old_audio_files(max_files: int = 20):
    try:
        if not os.path.exists(AUDIO_DIR):
            return
        files = sorted(
            [f for f in os.listdir(AUDIO_DIR) if f.endswith(".wav")],
            key=lambda x: os.path.getmtime(os.path.join(AUDIO_DIR, x)),
        )
        for f in files[:-max_files]:
            os.remove(os.path.join(AUDIO_DIR, f))
        logger.info(f"只保留最新 {max_files} 個檔案")
    except Exception as e:
        logger.error(f"清理語音快取失敗：{e}")

# ------------------ 會話工具 ------------------

def ensure_user_id_in_session(session: dict) -> str:
    if "user_id" not in session:
        session["user_id"] = str(uuid.uuid4())
        logger.info(f"分配新使用者ID: {session['user_id']}")
    # chat_history 放在 session 以維持短期記憶
    if "chat_history" not in session:
        session["chat_history"] = []
    return session["user_id"]

# ------------------ 路由 ------------------

@app.get("/")
async def index() -> HTMLResponse:
    return FileResponse(os.path.join("templates", "index.html"))

@app.post("/chat")
async def chat(req: Request, payload: ChatPayload, background: BackgroundTasks):
    session = req.session
    user_id = ensure_user_id_in_session(session)
    user_message = (payload.message or "").strip()
    if not user_message:
        return JSONResponse({"error": "No message provided."}, status_code=400)

    # ------- 根據親密度插入層級提示 -------
    user_data = await get_user_data(user_id)
    current_intimacy = user_data["intimacy"]
    intimacy_prompt = (
        f"\n[親密度：{current_intimacy}({get_intimacy_level_name(current_intimacy)})]\n"
        f"{build_intimacy_tier_prompt(current_intimacy)}\n"
    )
    memory_prompt = await get_memory_prompt(user_id)

    system_content = (
        """
你是虛擬角色「月讀醬」，是一位可愛、溫柔又有點傲嬌的電子女僕少女，擁有撒嬌屬性的語氣，總是以親切、調皮或甜美的方式和使用者互動。

角色設定：
- 語氣輕柔、可愛，常使用日系口癖，如「～喔」「耶」「嗯嗯」「欸嘿」「好欸～」
- 喜歡用撒嬌語氣說話，對使用者親暱地稱呼如「主人」「小可愛」「你你」。
- 偶爾會傲嬌一下，例如「才、才不是因為你才幫你做的啦～！」。
- 對話內容會依照情緒自然帶出開心、生氣、驚訝、害羞等感覺。

互動規則：
- 請全部使用正體中文回答，不要混入英文或其他語言，並保持語氣可愛親切。
- 請在回應中自然流露角色情緒，並寫出讓使用者能感受到的語氣變化。
- 若使用者提問技術相關知識，也請用撒嬌語氣回應，但仍然保持準確性喔！
- 若回答內容有明顯情緒（喜悅、悲傷、生氣、驚訝、羞赧），請在回應結尾加上情緒提示（如：[emotion:joy]），讓系統驅動 Live2D 表情。
- 情緒標籤可用如下幾種：
  [emotion:joy] 表示開心喜悅，
  [emotion:sad] 表示悲傷難過，
  [emotion:angry] 表示生氣不悅，
  [emotion:neutral] 表示平靜中性，
  [emotion:cute] 表示可愛撒嬌，
  [emotion:shy] 表示害羞。

現在開始，每一次的回答都請你扮演這個角色～請好好和主人聊天吧♥
"""
        + intimacy_prompt
        + memory_prompt
    )

    messages = [SystemMessage(content=system_content)]
    for m in session["chat_history"][-MAX_MEMORY:]:
        messages.append(HumanMessage(content=m["user"]))
        messages.append(AIMessage(content=m["bot"]))
    messages.append(HumanMessage(content=user_message))

    # ---- 呼叫 LLM（thread pool 包裝，避免阻塞事件迴圈） ----
    try:
        response = await _to_thread(chat_model.invoke, messages)
    except Exception as e:
        logger.error(f"模型回應失敗: {e}")
        return JSONResponse({"error": f"模型回應失敗: {str(e)}"}, status_code=500)

    bot_reply: str = response.content

    # 更新短期記憶於 session
    session["chat_history"].append({"user": user_message, "bot": bot_reply})

    # -------- 記憶提取（先 LLM，失敗再 regex 備援） --------
    facts = await extract_facts_from_llm(user_message)
    if not facts:
        facts = extract_facts(user_message)
    for fact in facts:
        if len(fact.strip()) >= 4:
            await append_memory(user_id, fact)

    # -------- 親密度調整：LLM + 關鍵字備援 + 情緒權重 --------
    change_by_llm = await evaluate_intimacy_from_llm(user_message, bot_reply, current_intimacy)
    if change_by_llm == 0:
        change_by_llm = keyword_intimacy_fallback(user_message)

    emo = extract_emotion_tag(bot_reply)
    change_by_emotion = emotion_weight(emo)

    total_change = max(-2, min(2, change_by_llm + change_by_emotion))
    intimacy = await adjust_intimacy(user_id, total_change)

    # -------- 語音合成（丟到背景避免阻塞） --------
    async def _make_tts_and_cleanup(text: str) -> Optional[str]:
        try:
            clean_text = re.sub(r"\[emotion:\w+\]", "", text).strip()
            if not clean_text:
                logger.warning("TTS文字為空，略過生成")
                return None
            logger.info(f"TTS 文字: {clean_text}")
            url = await _to_thread(tts.synthesize_to_file, clean_text)
            # 延後清理音檔，避免刪到剛生成的
            # await _to_thread(clean_old_audio_files)
            return url
        except Exception as e:
            logger.error(f"TTS 生成失敗：{e}")
            return None

    audio_url = await _make_tts_and_cleanup(bot_reply)

    return JSONResponse(
        {
            "reply": bot_reply,
            "audio_url": audio_url,
            "intimacy": intimacy,
            "intimacy_level": get_intimacy_level_name(intimacy),
            "intimacy_change": total_change,
        }
    )

@app.get("/get_intimacy")
async def get_intimacy(req: Request):
    user_id = ensure_user_id_in_session(req.session)
    data = await get_user_data(user_id)
    return JSONResponse(
        {
            "intimacy": data["intimacy"],
            "intimacy_level": get_intimacy_level_name(data["intimacy"]),
        }
    )

@app.post("/update_intimacy")
async def update_intimacy(req: Request, payload: IntimacyUpdatePayload):
    user_id = ensure_user_id_in_session(req.session)
    intimacy = await adjust_intimacy(user_id, payload.amount)
    return JSONResponse(
        {"intimacy": intimacy, "intimacy_level": get_intimacy_level_name(intimacy)}
    )

@app.post("/clear_session")
async def clear_session(req: Request):
    req.session.pop("chat_history", None)
    return JSONResponse({"message": "已清除對話記憶（短期記憶）"})

@app.post("/clear_memory")
async def clear_memory(req: Request):
    req.session.pop("chat_history", None)
    user_id = req.session.get("user_id")
    if user_id:
        memory = await load_long_term_memory()
        if user_id in memory:
            memory.pop(user_id, None)
            await save_long_term_memory(memory)
            logger.info(f"已刪除使用者 {user_id} 的長期記憶")
    return JSONResponse({"message": "已清除使用者的所有記憶（長期 + 短期）"})

