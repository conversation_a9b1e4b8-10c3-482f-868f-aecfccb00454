{"Version": 3, "Meta": {"Duration": 2.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 35, "TotalSegmentCount": 133, "TotalPointCount": 364, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.344, 0, 0.389, 0, 0.433, 0, 1, 0.522, 0, 0.611, 0, 0.7, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.344, 0, 0.389, -15, 0.433, -15, 1, 0.522, -15, 0.611, 11, 0.7, 11, 0, 2.1, 11]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.411, 0, 0.522, -15, 0.633, -15, 1, 0.689, -15, 0.744, -9, 0.8, -9, 0, 2.1, -9]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.211, 1, 0.256, 1, 0.3, 1, 1, 0.422, 1, 0.544, 0, 0.667, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.422, 0, 0.544, 1, 0.667, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.211, 1, 0.256, 1, 0.3, 1, 1, 0.422, 1, 0.544, 0, 0.667, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.422, 0, 0.544, 1, 0.667, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.422, 0, 0.544, -0.14, 0.667, -0.14, 0, 2.1, -0.14]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, 0, 0.3, 0, 1, 0.422, 0, 0.544, -0.14, 0.667, -0.14, 0, 2.1, -0.14]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, -0.21, 0.667, -0.21, 0, 2.1, -0.21]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, -0.2, 0.667, -0.2, 0, 2.1, -0.2]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, 0.25, 0.667, 0.25, 0, 2.1, 0.25]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, 0.23, 0.667, 0.23, 0, 2.1, 0.23]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, 0, 0.667, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, 0, 0.667, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.244, 1, 0.322, 1, 0.4, 1, 1, 0.489, 1, 0.578, 1, 0.667, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.489, 0, 0.578, 1, 0.667, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 8, 0.4, 8, 1, 0.467, 8, 0.533, -7, 0.6, -7, 1, 0.656, -7, 0.711, 3, 0.767, 3, 1, 0.811, 3, 0.856, 0, 0.9, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.478, 0, 0.556, 4, 0.633, 4, 1, 0.689, 4, 0.744, 2, 0.8, 2, 0, 2.1, 2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamShoulder", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, -1, 0.4, -1, 1, 0.467, -1, 0.533, 1, 0.6, 1, 1, 0.656, 1, 0.711, -0.4, 0.767, -0.4, 1, 0.922, -0.4, 1.078, 0, 1.233, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamLeg", "Segments": [0, 1, 0, 2.1, 1]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -5.208, 0.3, -5.208, 1, 0.367, -5.208, 0.433, 9.583, 0.5, 9.583, 0, 2.1, 9.583]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -4.583, 0.3, -4.583, 1, 0.367, -4.583, 0.433, 4.375, 0.5, 4.375, 0, 2.1, 4.375]}, {"Target": "Parameter", "Id": "ParamHandLB", "Segments": [0, 9.971, 1, 0.111, 9.768, 0.222, 8.46, 0.333, 4.167, 1, 0.378, 2.449, 0.422, -4.583, 0.467, -4.583, 1, 0.511, -4.583, 0.556, 4.792, 0.6, 4.792, 0, 2.1, 4.792]}, {"Target": "Parameter", "Id": "ParamHandRB", "Segments": [0, 9.971, 1, 0.111, 9.768, 0.222, 8.46, 0.333, 4.167, 1, 0.378, 2.449, 0.422, -4.583, 0.467, -4.583, 1, 0.511, -4.583, 0.556, 7.292, 0.6, 7.292, 0, 2.1, 7.292]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.211, 0, 0.256, -0.142, 0.3, 0.234, 1, 0.378, 0.89, 0.456, 10, 0.533, 10, 1, 0.578, 10, 0.622, -10, 0.667, -10, 1, 0.733, -10, 0.8, 9.951, 0.867, 9.951, 1, 0.944, 9.951, 1.022, -9.508, 1.1, -9.508, 1, 1.178, -9.508, 1.256, 5.892, 1.333, 5.892, 1, 1.4, 5.892, 1.467, -2, 1.533, -2, 1, 1.578, -2, 1.622, 1, 1.667, 1, 1, 1.722, 1, 1.778, 0, 1.833, 0, 0, 2.1, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 0, 0, 2.1, 0]}, {"Target": "PartOpacity", "Id": "PartArmB", "Segments": [0, 0.99, 0, 2.1, 0.99]}]}